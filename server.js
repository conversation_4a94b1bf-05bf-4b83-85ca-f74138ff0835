const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Import question data
const questionData = require('./data/questions.js');

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
app.use(compression());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Store active exam sessions
const examSessions = new Map();

// Routes

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Get exam types
app.get('/api/exam-types', (req, res) => {
    const examTypes = Object.keys(questionData).map(type => ({
        id: type,
        name: type.toUpperCase(),
        description: getExamDescription(type),
        questionCount: questionData[type].length
    }));
    res.json(examTypes);
});

// Start new exam session
app.post('/api/exam/start', (req, res) => {
    const { examType, questionCount = 50 } = req.body;
    
    if (!questionData[examType]) {
        return res.status(400).json({ error: 'Invalid exam type' });
    }
    
    const sessionId = generateSessionId();
    const questions = getRandomQuestions(examType, questionCount);
    
    const session = {
        id: sessionId,
        examType,
        questions,
        currentQuestionIndex: 0,
        answers: [],
        startTime: new Date(),
        score: 0,
        completed: false
    };
    
    examSessions.set(sessionId, session);
    
    res.json({
        sessionId,
        examType,
        totalQuestions: questions.length,
        timePerQuestion: 15
    });
});

// Get current question
app.get('/api/exam/:sessionId/question/:index', (req, res) => {
    const { sessionId, index } = req.params;
    const session = examSessions.get(sessionId);
    
    if (!session) {
        return res.status(404).json({ error: 'Session not found' });
    }
    
    const questionIndex = parseInt(index);
    if (questionIndex < 0 || questionIndex >= session.questions.length) {
        return res.status(400).json({ error: 'Invalid question index' });
    }
    
    const question = session.questions[questionIndex];
    const questionData = {
        id: question.id,
        question: question.question,
        options: question.options,
        category: question.category,
        difficulty: question.difficulty,
        questionNumber: questionIndex + 1,
        totalQuestions: session.questions.length
    };
    
    res.json(questionData);
});

// Submit answer
app.post('/api/exam/:sessionId/answer', (req, res) => {
    const { sessionId } = req.params;
    const { questionIndex, selectedOption, timeSpent } = req.body;
    
    const session = examSessions.get(sessionId);
    if (!session) {
        return res.status(404).json({ error: 'Session not found' });
    }
    
    const question = session.questions[questionIndex];
    const isCorrect = selectedOption === question.correctAnswer;
    
    const answerData = {
        questionIndex,
        selectedOption,
        correctAnswer: question.correctAnswer,
        isCorrect,
        timeSpent,
        explanation: question.explanation
    };
    
    session.answers[questionIndex] = answerData;
    
    if (isCorrect) {
        session.score++;
    }
    
    res.json({
        isCorrect,
        correctAnswer: question.correctAnswer,
        explanation: question.explanation,
        score: session.score,
        totalQuestions: session.questions.length
    });
});

// Get exam results
app.get('/api/exam/:sessionId/results', (req, res) => {
    const { sessionId } = req.params;
    const session = examSessions.get(sessionId);
    
    if (!session) {
        return res.status(404).json({ error: 'Session not found' });
    }
    
    session.completed = true;
    session.endTime = new Date();
    
    const totalTime = Math.round((session.endTime - session.startTime) / 1000);
    const percentage = Math.round((session.score / session.questions.length) * 100);
    
    const results = {
        sessionId,
        examType: session.examType,
        score: session.score,
        totalQuestions: session.questions.length,
        percentage,
        totalTime,
        answers: session.answers,
        grade: getGrade(percentage)
    };
    
    res.json(results);
});

// Helper functions
function getExamDescription(type) {
    const descriptions = {
        ssc: 'Staff Selection Commission',
        psc: 'Public Service Commission',
        net: 'National Eligibility Test',
        ktet: 'Kerala Teacher Eligibility Test'
    };
    return descriptions[type] || type.toUpperCase();
}

function generateSessionId() {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

function getRandomQuestions(examType, count) {
    const questions = [...questionData[examType]];
    const shuffled = questions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, questions.length));
}

function getGrade(percentage) {
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C';
    return 'F';
}

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Exam Questionnaire Server running on port ${PORT}`);
    console.log(`📚 Access the application at http://localhost:${PORT}`);
});

module.exports = app;
